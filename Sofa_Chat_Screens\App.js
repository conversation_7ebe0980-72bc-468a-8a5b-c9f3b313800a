import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from "@expo/vector-icons";
import { useFonts } from "expo-font";
import {
  Tajawal_300Light,
  Tajawal_400Regular,
  Tajawal_500Medium,
  Tajawal_700Bold,
  Tajawal_800ExtraBold,
} from "@expo-google-fonts/tajawal";
import * as SplashScreenExpo from "expo-splash-screen";
import { useEffect } from "react";

import SplashScreen from "./screens/SplashScreen";
import ChatScreen from "./screens/ChatScreen";
import SettingScreen from "./screens/SettingScreen";
import ServerSignIn from "./screens/AuthScreens/ServerSignIn";

// Keep the splash screen visible while we fetch resources
SplashScreenExpo.preventAutoHideAsync();

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: "#1E1E1E",
        tabBarInactiveTintColor: "#555",
        tabBarLabelStyle: {
          fontFamily: "Tajawal_500Medium",
          fontSize: 12,
        },
      }}
    >
      <Tab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="chatbubbles" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="settings" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

export default function App() {
  let [fontsLoaded, fontError] = useFonts({
    Tajawal: Tajawal_400Regular,
    "Tajawal-Light": Tajawal_300Light,
    "Tajawal-Medium": Tajawal_500Medium,
    "Tajawal-Bold": Tajawal_700Bold,
    "Tajawal-ExtraBold": Tajawal_800ExtraBold,
  });

  useEffect(() => {
    if (fontsLoaded || fontError) {
      console.log("Fonts loaded:", fontsLoaded);
      console.log("Font error:", fontError);
      SplashScreenExpo.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen
          name="Splash"
          component={SplashScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ServerSignIn"
          component={ServerSignIn}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MainTabs"
          component={MainTabs}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
