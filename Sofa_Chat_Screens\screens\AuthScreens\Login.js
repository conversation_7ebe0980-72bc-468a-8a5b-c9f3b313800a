import {
  View,
  Text,
  TextInput,
  Pressable,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  I18nManager,
} from "react-native";
import { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { CommonHeader } from "../../components/CommonHeader";
import Colors from "../../components/Colors";
import { useNavigation } from "@react-navigation/native";

const { height, width } = Dimensions.get("window");

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const LogIn = () => {
  const navigation = useNavigation();
  const [serverUrl, setServerUrl] = useState("");
  const [serverName, setServerName] = useState("");

  const handleConnect = () => {
    console.log("Loged In");
    navigation.replace("MainTabs");
  };

  return (
    <LinearGradient
      colors={["#E5F9F6", "#FFFFFF", "#E5F9F6", "#E5F9F6"]}
      style={styles.LinearGradientContainer}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <CommonHeader />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.content}>
            {/* Header */}
            <Text style={styles.subtitle}>تسجيل الدخول</Text>
            <Text style={styles.instruction}>
                ادخل البيانات الخاصة بك للدخول.
            </Text>

            {/* Form */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>البريد الإلكتروني أو اسم المستخدم</Text>
              <TextInput
                style={styles.input}
                placeholder="URL"
                placeholderTextColor="#999"
                value={serverUrl}
                onChangeText={setServerUrl}
                textAlign="right" // Right align for Arabic
                textAlignVertical="center"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>كلمة المرور</Text>
              <TextInput
                style={styles.input}
                placeholder="كلمة المرور"
                placeholderTextColor="#999"
                value={serverName}
                onChangeText={setServerName}
                textAlign="right" // Right align for Arabic
                textAlignVertical="center"
              />
            </View>

            <Pressable
              style={styles.connectButton}
              onPress={handleConnect}
              android_ripple={{
                color: Colors.primary50,
                overflow: "hidden",
              }}
            >
              <Text style={styles.connectButtonText}>تسجيل الدخول</Text>
            </Pressable>
            <Pressable
              style={styles.DidButton}
              onPress={handleConnect}
              android_ripple={{
                color: Colors.primary50,
                overflow: "hidden",
              }}
            >
              <Text style={styles.DidButtonText}>تسجيل الدخول بالهوية الرقمية</Text>
            </Pressable>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default LogIn;

const styles = StyleSheet.create({
  LinearGradientContainer: {
    flex: 1,
    alignItems: "center",
    paddingTop: height * 0.08,
    paddingBottom: height * 0.14,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: "center",
  },
  content: {
    width: width * 0.9,
    alignSelf: "center",
    padding: 16,
    borderRadius: 16,
  },
  subtitle: {
    fontSize: 18,
    textAlign: "right",
    marginBottom: 4,
    color: "#2C3E50",
    writingDirection: "rtl",
    fontFamily: "Tajawal_700Bold",
  },
  instruction: {
    fontSize: 15,
    color: "#7F8C8D",
    textAlign: "right",
    marginBottom: 18,
    writingDirection: "rtl",
    lineHeight: 16,
    fontFamily: "Tajawal",
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 10,
    color: "#34495E",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "Tajawal_700Bold",
  },
  input: {
    borderWidth: 1,
    borderColor: "#CCCCCC",
    borderRadius: 10,
    padding: 8,
    fontSize: 14,
    height: 50,
    width: "110%",
    marginLeft: -10,
    backgroundColor: "#FFFFFF",
    alignContent: "center",
    textAlign: "right",
    writingDirection: "rtl",
    fontFamily: "Tajawal",
  },
  connectButton: {
    backgroundColor: Colors.primary700,
    borderRadius: 10,
    padding: 16,
    alignItems: "center",
    marginTop: 20,
    height: 60,
    justifyContent: "center",
  },
  connectButtonText: {
    color: Colors.primary50,
    fontSize: 18,
    fontFamily: "Tajawal_500Medium",
  },
  DidButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.gray500,
    borderRadius: 10,
    padding: 4,
    alignItems: "center",
    marginTop: 20,
    height: 40,
    justifyContent: "center",
  },
  DidButtonText: {
    color: Colors.gray700,
    fontSize: 17,
    fontFamily: "Tajawal_500Medium",
  },
});
