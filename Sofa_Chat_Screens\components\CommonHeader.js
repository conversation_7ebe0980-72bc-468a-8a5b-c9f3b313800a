import { View, Text, Image, StyleSheet } from "react-native";
import { TopRightRectangles } from "./CornerRectangles";

export const CommonHeader = () => {
  return (
    <>
      <TopRightRectangles />
      <View style={styles.header}>
        <Image
          source={require("../assets/Sofa-Chat/sofa-chat-logo.png")}
          style={styles.logo}
        />
        <Text style={styles.appName}>سوفا شات</Text>
      </View>
      </>
      );
};
const styles = StyleSheet.create({
  header: {
    flexDirection: "row-reverse",
    alignItems: "center",
    justifyContent: "center",
    alignContent: "center",
    width: "100%",
    paddingHorizontal: 24,
    marginTop: 13,
    zIndex: 1,
  },
  logo: {
    width: 35,
    height: 35,
    marginLeft: 10,
  },
  appName: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1E1E1E",
    fontFamily: "NotoSansArabic-Bold",
  },
});
