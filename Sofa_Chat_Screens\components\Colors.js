// theme.js
export default {
  // Extracted from screenshot
 primary50: '#f0f5f3',  // Lightest background tint
    primary100: '#d6e4e0', // Soft fill for cards
    primary200: '#a8c5bd',  // Subtle borders
    primary400: '#6b9080',  // Your base color (buttons, accents)
    primary500: '#4a7a6b',  // Saturated for active states
    primary700: '#2d5c50',  // Darker for text/headings
    primary800: '#1a3a32',  // Darkest (e.g., app bar)
    primary900: '#0d1117',  // Darkest (e.g., app bar)
    text: '#000000',           // Main text color
    subtext: '#666666',        // Instruction text
    inputBorder: '#CCCCCC',    // Input field border
    inputPlaceholder: '#999999',// Placeholder text

  // Complementary colors for theme
  secondary: '#5AC8FA',      // Light blue for secondary elements
  success: '#4CD964',        // Green for success states
  warning: '#FFCC00',        // Yellow for warnings
  danger: '#FF3B30',        // Red for errors/destructive actions
  accent: '#5856D6',         // Purple for accents
  lightText: '#FFFFFF',      // Text on dark backgrounds
  darkBackground: '#1C1C1E', // Dark background for dark mode
  cardDark: '#2C2C2E',       // Dark card background
  highlight: '#FF9500',      // Orange for highlights
  muted: '#8E8E93',          // Muted text/icons
  
  // Additional UI colors
  buttonDisabled: '#A7A7A7', // Disabled button color
  separator: '#E5E5EA',      // Separator lines
  overlay: 'rgba(0,0,0,0.5)' // Overlay color
};