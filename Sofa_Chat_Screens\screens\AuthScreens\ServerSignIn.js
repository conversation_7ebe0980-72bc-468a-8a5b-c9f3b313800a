import { View, Text, TextInput, TouchableOpacity, StyleSheet, SafeAreaView, KeyboardAvoidingView, Platform, Dimensions } from 'react-native';
import { useState } from 'react';
import { LinearGradient } from 'expo-linear-gradient';

import { CommonHeader } from '../../components/CommonHeader';

const { width, height } = Dimensions.get('window');

const ServerSignIn = () => {

  const [serverUrl, setServerUrl] = useState('');
  const [serverName, setServerName] = useState('');
  const [activeField, setActiveField] = useState(null);

  const keyboardKeys = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['2123', 'EN · LV', '→1']
  ];

  const handleKeyPress = (key) => {
    if (activeField === 'url') {
      setServerUrl(prev => prev + key);
    } else if (activeField === 'name') {
      setServerName(prev => prev + key);
    }
  };

  const handleConnect = () => {
    // Connection logic here
    console.log('Connecting to:', serverUrl, 'Name:', serverName);
  };

  return (
    <LinearGradient
          colors={['#E5F9F6', '#FFFFFF', '#E5F9F6']}
          style={styles.LinearGradientContainer}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
        >
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
          <CommonHeader />
        <View style={styles.content}>
          {/* Header */}
          <Text style={styles.title}>سوفا شات</Text>
          <Text style={styles.subtitle}>الاتصال باخادم</Text>
          <Text style={styles.instruction}>قم بإعداد اخادم الأول الخاص بك</Text>

          {/* Form */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>عنوان الـRUM للخادم</Text>
            <TextInput
              style={[styles.input, activeField === 'url' && styles.activeInput]}
              placeholder="URL"
              placeholderTextColor="#999"
              value={serverUrl}
              onChangeText={setServerUrl}
              onFocus={() => setActiveField('url')}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>اسم عرض اخادم</Text>
            <TextInput
              style={[styles.input, activeField === 'name' && styles.activeInput]}
              placeholder="الاسم الذي سيتم عرضه في قائمة أفوادم"
              placeholderTextColor="#999"
              value={serverName}
              onChangeText={setServerName}
              onFocus={() => setActiveField('name')}
            />
          </View>

          <TouchableOpacity style={styles.connectButton} onPress={handleConnect}>
            <Text style={styles.connectButtonText}>انصال</Text>
          </TouchableOpacity>
        </View>

        {/* Custom Keyboard */}
        <View style={styles.keyboard}>
          {keyboardKeys.map((row, rowIndex) => (
            <View key={rowIndex} style={styles.keyboardRow}>
              {row.map((key, keyIndex) => (
                <TouchableOpacity
                  key={keyIndex}
                  style={styles.keyButton}
                  onPress={() => handleKeyPress(key)}
                >
                  <Text style={styles.keyText}>{key}</Text>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
    </LinearGradient>
  );
};

export default ServerSignIn;

const styles = StyleSheet.create({
  LinearGradientContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: height * 0.08,
    paddingBottom: height * 0.12,
  },  
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 10,
  },
  subtitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 5,
  },
  instruction: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
  },
  activeInput: {
    borderColor: '#007AFF',
  },
  connectButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  keyboard: {
    backgroundColor: '#F0F0F0',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#CCCCCC',
  },
  keyboardRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
  },
  keyButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    padding: 10,
    marginHorizontal: 3,
    minWidth: 30,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  keyText: {
    fontSize: 16,
  },
});


// const AuthScreen = () => {
  
// };