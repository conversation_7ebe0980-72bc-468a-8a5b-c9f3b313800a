import {
  View,
  Text,
  TextInput,
  Pressable,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  I18nManager
} from "react-native";
import { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { CommonHeader } from "../../components/CommonHeader";
import Colors from "../../components/Colors";

const { height, width } = Dimensions.get("window");

// Force RTL layout for Arabic
I18nManager.forceRTL(true);
I18nManager.allowRTL(true);

const ServerSignIn = () => {
  const [serverUrl, setServerUrl] = useState("");
  const [serverName, setServerName] = useState("");

  const handleConnect = () => {
    console.log("Connecting to:", serverUrl, "Name:", serverName);
  };

  return (
    <LinearGradient
      colors={["#E5F9F6", "#FFFFFF", "#E5F9F6", "#E5F9F6"]}
      style={styles.LinearGradientContainer}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
    >
      <CommonHeader />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.content}>
            {/* Header */}
            <Text style={styles.subtitle}>الاتصال بالخادم</Text>
            <Text style={styles.instruction}>
              قم بإعداد الخادم الأول الخاص بك
            </Text>

            {/* Form */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>عنوان الـ URL للخادم</Text>
              <TextInput
                style={styles.input}
                placeholder="URL"
                placeholderTextColor="#999"
                value={serverUrl}
                onChangeText={setServerUrl}
                textAlign="right" // Right align for Arabic
                textAlignVertical="center"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>اسم عرض الخادم</Text>
              <TextInput
                style={styles.input}
                placeholder="الاسم الذي سيتم عرضه في قائمة الخوادم"
                placeholderTextColor="#999"
                value={serverName}
                onChangeText={setServerName}
                textAlign="right" // Right align for Arabic
                textAlignVertical="center"
              />
            </View>

            <Pressable
              style={styles.connectButton}
              onPress={handleConnect}
              android_ripple={{ color: Colors.primary200, overFlow: false,bo }}
            >
              <Text style={styles.connectButtonText}>اتصال</Text>
            </Pressable>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default ServerSignIn;

const styles = StyleSheet.create({
  LinearGradientContainer: {
    flex: 1,
    alignItems: "center",
    paddingTop: height * 0.08,
    paddingBottom: height * 0.12,
  },
  container: {
    flex: 1,
    width: '100%',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'center',
  },
  content: {
    width: width * 0.9,
    alignSelf: 'center',
    padding: 20,
    borderRadius: 16,
  },
  subtitle: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: "right",
    marginBottom: 8,
    color: '#2C3E50',
    writingDirection: 'rtl',
  },
  instruction: {
    fontSize: 15,
    color: "#7F8C8D",
    textAlign: "right",
    marginBottom: 30,
    writingDirection: 'rtl',
    lineHeight: 22,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: '600',
    color: '#34495E',
    textAlign: "right",
    writingDirection: 'rtl',
  },
  input: {
    borderWidth: 1,
    borderColor: "#CCCCCC",
    borderRadius: 10,
    padding: 16,
    fontSize: 16,
    height: 50,
    width: '100%',
    backgroundColor: '#FFFFFF',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  connectButton: {
    backgroundColor: Colors.primary700,
    borderRadius: 10,
    padding: 16,
    alignItems: "center",
    marginTop: 20,
    height: 60,
    justifyContent: 'center',
  },
  connectButtonText: {
    color: Colors.primary50,
    fontSize: 18,
    fontWeight: "bold",
  },
});